#include "esp.hpp"
#include "../../../pch.h" // Make sure this includes <algorithm> for std::min/max or undefines macros
#include "../../../ItemIndex.hpp"
#include "../../globals.hpp"

#include <algorithm> // For std::min and std::max
#include <cmath>     // For M_PI, cosf, sinf (ensure _USE_MATH_DEFINES is defined if M_PI is missing)

using namespace globals;

void ESP::RenderESP( HANDLE driverHandle, const Reader& reader ) {
  const auto* gameVars = GameVars::getInstance();

  auto playerList = reader.getPlayerListCopy();
  auto entityList = reader.getEntityListCopy();

  uintptr_t clientBase = gameVars->getClient();
  void* drv = gameVars->getDriver();

  ImGui::GetStyle().AntiAliasedLines = false;
  ImGui::GetStyle().AntiAliasedFill = false;
  ImGui::GetStyle().AntiAliasedLinesUseTex = false;

  view_matrix_t viewMatrix = driver::read_memory<view_matrix_t>(
    gameVars->getDriver(),
    gameVars->getClient() + Offset::Matrix );

  uintptr_t localPlayer = driver::read_memory<uintptr_t>( gameVars->getDriver(), gameVars->getClient() + Offset::LocalPlayerController );

  Vector currentViewAngles = driver::read_memory<Vector>(drv, clientBase + 0x1A6A2C0); // dwViewAngles

  uintptr_t localPlayerPawn = driver::read_memory<uintptr_t>(drv, clientBase + 0x184B0D0); // dwLocalPlayerPawn

  int numShots = driver::read_memory<int>(drv, localPlayerPawn + 0x23FC); // m_iShotsFired

  Vector aimPunchAngle = driver::read_memory<Vector>(drv, localPlayerPawn + 0x1584); // m_aimPunchAngle

  Vector recoilAdjustedViewAngles = currentViewAngles;
  recoilAdjustedViewAngles.x += aimPunchAngle.x * 2.f;
  recoilAdjustedViewAngles.y += aimPunchAngle.y * 2.f;

  Vector::Normalize(recoilAdjustedViewAngles);
  Vector forwardVec;
  Vector::Angle(recoilAdjustedViewAngles, forwardVec);

  forwardVec.x *= 10000.f;
  forwardVec.y *= 10000.f;
  forwardVec.z *= 10000.f;

  Vector pawnOrigin = driver::read_memory<Vector>(drv, localPlayerPawn + 0x1324); // m_vOldOrigin oder m_vecOrigin

  Vector viewOffset = driver::read_memory<Vector>(drv, localPlayerPawn + 0xCB0);

  Vector startPos = pawnOrigin;
  Vector::Add(startPos, viewOffset);

  Vector endPos = startPos;
  Vector::Add(endPos, forwardVec);

  Vector endScreen;
  Vector::world_to_screen(viewMatrix, endPos, endScreen);

  // Check if player is shooting over 1 bullet to draw the recoil crosshair.
  if (numShots > 1) {
      Render::Dot(endScreen.x, endScreen.y, 3.f, {255,0,0,255});
  }

  int localTeam = driver::read_memory<int>( gameVars->getDriver(), localPlayer + Offset::Pawn::iTeamNum );

  for ( const auto& player : playerList ) {
    // Check if player is enemy or teammate
    const bool isCurrentPlayerEnemy = (player.team != localTeam);

    // Skip teammates if "Ignore Teammates" is enabled
    if (!isCurrentPlayerEnemy && Esp::ignoreTeammates) continue;

    Vector playerWorldOrigin = driver::read_memory<Vector>( driverHandle, player.pCSPlayerPawn + Offset::Pawn::Pos );
    Vector playerViewOffset = driver::read_memory<Vector>(driverHandle, player.pCSPlayerPawn + 0xCB0);
    Vector playerWorldHeadPos = playerWorldOrigin;
    playerWorldHeadPos.z += playerViewOffset.z + 9;

    Vector screenHeadPos, screenFeetPos;
    if ( Vector::world_to_screen( viewMatrix, playerWorldHeadPos, screenHeadPos ) &&
      Vector::world_to_screen( viewMatrix, playerWorldOrigin, screenFeetPos ) ) {

      const float boxHeight = screenFeetPos.y - screenHeadPos.y;
      const float boxHalfWidth  = boxHeight * 0.25f;

      if (Esp::Health::Bar::enabled) {
        DrawPlayerHealth( viewMatrix, screenHeadPos, boxHeight, boxHalfWidth, player.health, player.entityId, isCurrentPlayerEnemy);
      }

      if (Esp::Armor::Bar::enabled) {
        const int currentArmor = player.armor;
        if (currentArmor > 0) {
          DrawPlayerArmor( viewMatrix, screenHeadPos, boxHeight, boxHalfWidth, currentArmor, player.entityId, isCurrentPlayerEnemy );
        }
      }

      if (Esp::Box::enabled) {
        if (Esp::Box::type == 0) {
          DrawPlayerBox( viewMatrix, screenHeadPos, screenFeetPos, isCurrentPlayerEnemy );
        } else {
          DrawPlayerCorneredBox( viewMatrix, screenHeadPos, screenFeetPos, isCurrentPlayerEnemy );
        }
      }

      if (Esp::Box::Filled::enabled) {
        DrawPlayerFilledBox( viewMatrix, screenHeadPos, screenFeetPos, isCurrentPlayerEnemy );
      }

      if (Esp::Snapline::enabled) {
        DrawPlayerSnapline( viewMatrix, screenHeadPos, screenFeetPos, isCurrentPlayerEnemy );
      }

      if (Esp::Info::enabled) {
        DrawPlayerInfo( viewMatrix, screenHeadPos, screenFeetPos, player.PlayerName, player.PlayerFlags, player.ItemDefinitionIndex, isCurrentPlayerEnemy );
      }

      if (Esp::Viewline::enabled) {
        Vector playerEyeAngles = driver::read_memory<Vector>( driverHandle, player.pCSPlayerPawn + Offset::Pawn::angEyeAngles );
        Vector playerEyeWorldPos = playerWorldOrigin;
        playerEyeWorldPos.z += playerViewOffset.z;
        DrawViewline( viewMatrix, playerEyeAngles, playerEyeWorldPos, isCurrentPlayerEnemy );
      }

      if (player.BoneArray) {
        if (Esp::Skeleton::enabled) {
          DrawPlayerSkeleton(viewMatrix, player.BoneArray, isCurrentPlayerEnemy);

          //if (Esp::Skeleton::Dots::enabled) {
            DrawPlayerJoints(viewMatrix, player.BoneArray, isCurrentPlayerEnemy);
          //}

          if (Esp::Skeleton::Head::enabled) {
            DrawPlayerHead(viewMatrix, player.BoneArray, isCurrentPlayerEnemy);
          }
        }
      }
    }
  }

  for (const auto& entity : entityList) {
    if ( entity.className.find( "_projectile" ) == std::string::npos)
      continue;

    // Placeholder for isEnemy logic for projectiles; typically false.
    const bool isCurrentProjectileEnemy = false;

    std::string cleanProjectileName = RemoveSuffix( entity.className, "_projectile" );
    uintptr_t   gameSceneNode       = driver::read_memory<uintptr_t>( driverHandle, entity.BaseEntity + Offset::Pawn::GameSceneNode );
    Vector      projectileOrigin    = driver::read_memory<Vector>( driverHandle, gameSceneNode + Offset::vecAbsOrigin );

    if ( projectileOrigin.isInvalid() )
      continue;

    Vector projectileScreenPos;
    if (Vector::world_to_screen(viewMatrix, projectileOrigin, projectileScreenPos)) {
      DrawProjectile(viewMatrix, projectileScreenPos, cleanProjectileName, isCurrentProjectileEnemy);
    }
  }
}

void ESP::DrawPlayerBox(const view_matrix_t& viewmatrix, const Vector& screenHead, const Vector& screenFeet, bool isEnemy) {
  const float boxHeight = screenFeet.y - screenHead.y;
  const float boxHalfVisualWidth  = boxHeight * 0.25f;

  const float boxLeftEdgeX  = screenHead.x - boxHalfVisualWidth;
  const float boxTopEdgeY   = screenHead.y;
  const float boxTotalVisualWidth = boxHalfVisualWidth * 2.0f;

  const ImVec4 mainBoxColor   = Esp::Box::Color;
  const ImVec4 outlineBoxColor = {0.f, 0.f, 0.f, 1.f};

  const float outlineThicknessPx = 1.0f;

  if (Esp::Box::outline) {
    const float outerOutlineX = boxLeftEdgeX - outlineThicknessPx;
    const float outerOutlineY = boxTopEdgeY - outlineThicknessPx;
    const float outerOutlineWidth = boxTotalVisualWidth + (outlineThicknessPx * 2.0f);
    const float outerOutlineHeight = boxHeight + (outlineThicknessPx * 2.0f);
    Render::DrawRect(outerOutlineX, outerOutlineY, outerOutlineWidth, outerOutlineHeight, outlineBoxColor, 0.f, 1.f);

    const float innerOutlineX = boxLeftEdgeX + outlineThicknessPx;
    const float innerOutlineY = boxTopEdgeY + outlineThicknessPx;
    const float innerOutlineWidth = boxTotalVisualWidth - (outlineThicknessPx * 2.0f);
    const float innerOutlineHeight = boxHeight - (outlineThicknessPx * 2.0f);
    Render::DrawRect(innerOutlineX, innerOutlineY, innerOutlineWidth, innerOutlineHeight, outlineBoxColor, 0.f, 1.f);
  }

  Render::DrawRect(boxLeftEdgeX, boxTopEdgeY, boxTotalVisualWidth, boxHeight, mainBoxColor, 0.f, 1.f);
}

void ESP::DrawPlayerFilledBox(const view_matrix_t& viewmatrix, const Vector& screenHead, const Vector& screenFeet, bool isEnemy) {
  const float boxHeight = screenFeet.y - screenHead.y;
  const float boxHalfVisualWidth = boxHeight * 0.25f;

  const float boxLeftEdgeX  = screenHead.x - boxHalfVisualWidth;
  const float boxTopEdgeY   = screenHead.y;
  const float boxTotalVisualWidth = boxHalfVisualWidth * 2.0f;

  const ImVec4 topFillColor    = Esp::Box::Filled::Color;
  const ImVec4 bottomFillColor = Esp::Box::Filled::Color2;

  Render::DrawRectFilledMultiColor(
    boxLeftEdgeX,
    boxTopEdgeY,
    boxTotalVisualWidth,
    boxHeight,
    topFillColor,
    bottomFillColor
  );
}

void ESP::DrawPlayerCorneredBox(const view_matrix_t& viewmatrix, const Vector& screenHead, const Vector& screenFeet, bool isEnemy) {
  const float boxHeight           = screenFeet.y - screenHead.y;
  const float boxHalfVisualWidth  = boxHeight * 0.25f;
  const float cornerLineLength    = boxHalfVisualWidth * 0.5f;

  const float leftEdgeX   = screenHead.x - boxHalfVisualWidth;
  const float rightEdgeX  = screenHead.x + boxHalfVisualWidth;
  const float topEdgeY    = screenHead.y;
  const float bottomEdgeY = screenHead.y + boxHeight;

  const ImVec4 mainCornerColor  = Esp::Box::Color;
  const ImVec4 outlineCornerColor = {0.f, 0.f, 0.f, 1.f};

  const std::vector<std::pair<int, int>> fixedOutlineOffsets = {
    {-1, -1}, {1, -1}, {-1, 1}, {1, 1}
  };
  const float outlineLineThickness = 1.f;

  if (Esp::Box::outline) {
  for (const auto& offsetPair : fixedOutlineOffsets) {
    const float dx = static_cast<float>(offsetPair.first);
    const float dy = static_cast<float>(offsetPair.second);

    Render::Line(leftEdgeX + dx, topEdgeY + dy, leftEdgeX + cornerLineLength + dx, topEdgeY + dy, outlineCornerColor, outlineLineThickness);
    Render::Line(leftEdgeX + dx, topEdgeY + dy, leftEdgeX + dx, topEdgeY + cornerLineLength + dy, outlineCornerColor, outlineLineThickness);

    Render::Line(rightEdgeX + dx, topEdgeY + dy, rightEdgeX - cornerLineLength + dx, topEdgeY + dy, outlineCornerColor, outlineLineThickness);
    Render::Line(rightEdgeX + dx, topEdgeY + dy, rightEdgeX + dx, topEdgeY + cornerLineLength + dy, outlineCornerColor, outlineLineThickness);

    Render::Line(leftEdgeX + dx, bottomEdgeY + dy, leftEdgeX + cornerLineLength + dx, bottomEdgeY + dy, outlineCornerColor, outlineLineThickness);
    Render::Line(leftEdgeX + dx, bottomEdgeY + dy, leftEdgeX + dx, bottomEdgeY - cornerLineLength + dy, outlineCornerColor, outlineLineThickness);

    Render::Line(rightEdgeX + dx, bottomEdgeY + dy, rightEdgeX - cornerLineLength + dx, bottomEdgeY + dy, outlineCornerColor, outlineLineThickness);
    Render::Line(rightEdgeX + dx, bottomEdgeY + dy, rightEdgeX + dx, bottomEdgeY - cornerLineLength + dy, outlineCornerColor, outlineLineThickness);
  }
  }

  const float mainLineThickness = 1.f;
  Render::Line(leftEdgeX, topEdgeY, leftEdgeX + cornerLineLength, topEdgeY, mainCornerColor, mainLineThickness);
  Render::Line(leftEdgeX, topEdgeY, leftEdgeX, topEdgeY + cornerLineLength, mainCornerColor, mainLineThickness);

  Render::Line(rightEdgeX, topEdgeY, rightEdgeX - cornerLineLength, topEdgeY, mainCornerColor, mainLineThickness);
  Render::Line(rightEdgeX, topEdgeY, rightEdgeX, topEdgeY + cornerLineLength, mainCornerColor, mainLineThickness);

  Render::Line(leftEdgeX, bottomEdgeY, leftEdgeX + cornerLineLength, bottomEdgeY, mainCornerColor, mainLineThickness);
  Render::Line(leftEdgeX, bottomEdgeY, leftEdgeX, bottomEdgeY - cornerLineLength, mainCornerColor, mainLineThickness);

  Render::Line(rightEdgeX, bottomEdgeY, rightEdgeX - cornerLineLength, bottomEdgeY, mainCornerColor, mainLineThickness);
  Render::Line(rightEdgeX, bottomEdgeY, rightEdgeX, bottomEdgeY - cornerLineLength, mainCornerColor, mainLineThickness);

}

void ESP::DrawPlayerHealth(const view_matrix_t& viewmatrix, const Vector& topScreenPos, float playerBoxHeight, float playerBoxHalfWidth, int healthValue, int entityId, bool isEnemy) {
  if (Esp::Health::Bar::Style::type == Esp::HealthBarStyle::Solid) {
    DrawPlayerHealthBarSolid(viewmatrix, topScreenPos, playerBoxHeight, playerBoxHalfWidth, healthValue, entityId, isEnemy);
  } else {
    DrawPlayerHealthBarReactive(viewmatrix, topScreenPos, playerBoxHeight, playerBoxHalfWidth, healthValue, entityId, isEnemy);
  }
}

void ESP::DrawPlayerHealthBarReactive(const view_matrix_t& viewmatrix, const Vector& topScreenPos, float playerBoxHeight, float playerBoxHalfWidth, int healthValue, int entityId, bool isEnemy) {
  const float animatedHealth = HealthBarAnimator::UpdateAnimation(entityId, static_cast<float>(healthValue));
  // FIXED: Wrapped std::min and std::max in parentheses to avoid macro conflicts
  const float healthRatioClamped = (std::min)(1.f, (std::max)(0.f, animatedHealth / 100.f));

  const float barFullHeight = playerBoxHeight;
  const float filledBarActualHeight = barFullHeight * healthRatioClamped;
  const float barLeftOffset = 5.f;
  const float barPositionX = (topScreenPos.x - playerBoxHalfWidth) - barLeftOffset;
  const float barPositionY = topScreenPos.y;
  const float barVisualWidth = 2.f;
  const float backgroundVisualWidth = 4.f;
  const float backgroundBorderOffset = 1.f;

  const std::string healthValueText = std::to_string(static_cast<int>(animatedHealth));

  const ImVec4 currentBarReactiveColor = {
    1.0f - healthRatioClamped,
    healthRatioClamped,
    0.0f,
    1.0f
  };
  const ImVec4 barBackgroundColor = {0.f, 0.f, 0.f, 1.f};

  if (Esp::Health::Bar::Glow::enabled) {
    const ImVec4 glowEffectColor = {
      currentBarReactiveColor.x,
      currentBarReactiveColor.y,
      currentBarReactiveColor.z,
      0.7f
    };
    const int glowBlurRadius = 7;
    const int glowSpread = 2;
    Render::DrawRectGlow(
      barPositionX,
      barPositionY,
      barVisualWidth,
      barFullHeight,
      currentBarReactiveColor,
      glowEffectColor,
      glowBlurRadius,
      glowSpread
    );
  }

  const float bgRectX = barPositionX - backgroundBorderOffset;
  const float bgRectY = barPositionY - backgroundBorderOffset;
  const float bgRectWidth = backgroundVisualWidth;
  const float bgRectHeight = barFullHeight + (backgroundBorderOffset * 2.0f);
  Render::DrawRectFilled(bgRectX, bgRectY, bgRectWidth, bgRectHeight, barBackgroundColor, 0);

  const float filledBarRenderY = barPositionY + (barFullHeight - filledBarActualHeight);
  Render::DrawRectFilled(barPositionX, filledBarRenderY, barVisualWidth, filledBarActualHeight, currentBarReactiveColor, 0);

  if (Esp::Health::Value::enabled && animatedHealth < 100) {
    ImGui::PushFont(espfont);
    const float healthTextHorizontalOffset = (static_cast<int>(animatedHealth) <= 9) ? 3.f : 7.f;
    const float textRenderPosX = barPositionX - healthTextHorizontalOffset + 1.f;
    const float textRenderPosY = filledBarRenderY - 6.f;
    const ImVec4 healthTextColor = Esp::Health::Value::Color;

    Render::Text(textRenderPosX, textRenderPosY, healthTextColor, healthValueText, fontSize, font_flags_t::outline);
    ImGui::PopFont();
  }
}

void ESP::DrawPlayerHealthBarSolid(const view_matrix_t& viewmatrix, const Vector& topScreenPos, float playerBoxHeight, float playerBoxHalfWidth, int healthValue, int entityId, bool isEnemy) {
  const float animatedHealth = HealthBarAnimator::UpdateAnimation(entityId, static_cast<float>(healthValue));
  // FIXED: Wrapped std::min and std::max in parentheses
  const float healthRatioClamped = (std::min)(1.f, (std::max)(0.f, animatedHealth / 100.f));

  const float barFullHeight = playerBoxHeight;
  const float filledBarActualHeight = barFullHeight * healthRatioClamped;
  const float barLeftOffset = 5.f;
  const float barPositionX = (topScreenPos.x - playerBoxHalfWidth) - barLeftOffset;
  const float barPositionY = topScreenPos.y;
  const float barVisualWidth = 2.f;
  const float backgroundVisualWidth = 4.f;
  const float backgroundBorderOffset = 1.f;

  const std::string healthValueText = std::to_string(static_cast<int>(animatedHealth));

  const ImVec4 solidBarColor = Esp::Health::Bar::Color;
  const ImVec4 barBackgroundColor = {0.f, 0.f, 0.f, 1.f};

  if (Esp::Health::Bar::Glow::enabled) {
    const ImVec4 glowEffectColor = {
      solidBarColor.x,
      solidBarColor.y,
      solidBarColor.z,
      0.7f
    };
    const int glowBlurRadius = 7;
    const int glowSpread = 2;
    Render::DrawRectGlow(
      barPositionX,
      barPositionY,
      barVisualWidth,
      barFullHeight,
      solidBarColor,
      glowEffectColor,
      glowBlurRadius,
      glowSpread
    );
  }

  const float bgRectX = barPositionX - backgroundBorderOffset;
  const float bgRectY = barPositionY - backgroundBorderOffset;
  const float bgRectWidth = backgroundVisualWidth;
  const float bgRectHeight = barFullHeight + (backgroundBorderOffset * 2.0f);
  Render::DrawRectFilled(bgRectX, bgRectY, bgRectWidth, bgRectHeight, barBackgroundColor, 0);

  const float filledBarRenderY = barPositionY + (barFullHeight - filledBarActualHeight);
  Render::DrawRectFilled(barPositionX, filledBarRenderY, barVisualWidth, filledBarActualHeight, solidBarColor, 0);

  if (Esp::Health::Value::enabled && animatedHealth < 100) {
    ImGui::PushFont(espfont);
    const float healthTextHorizontalOffset = (static_cast<int>(animatedHealth) <= 9) ? 3.f : 7.f;
    const float textRenderPosX = barPositionX - healthTextHorizontalOffset + 1.f;
    const float textRenderPosY = filledBarRenderY - 6.f;
    const ImVec4 healthTextColor = Esp::Health::Value::Color;

    Render::Text(textRenderPosX, textRenderPosY, healthTextColor, healthValueText, fontSize, font_flags_t::outline);
    ImGui::PopFont();
  }
}

void ESP::DrawPlayerArmor(const view_matrix_t& viewmatrix, const Vector& topScreenPos, float playerBoxHeight, float playerBoxHalfWidth, int armorValue, int entityId, bool isEnemy) {
  const float animatedArmor = ArmorBarAnimator::UpdateAnimation(entityId, static_cast<float>(armorValue));
  // FIXED: Wrapped std::min and std::max in parentheses
  const float armorRatioClamped = (std::min)(1.f, (std::max)(0.f, animatedArmor / 100.f));

  const float barFullVisualWidth = playerBoxHalfWidth * 2.f;
  const float filledBarActualWidth = barFullVisualWidth * armorRatioClamped;
  const float barStartX = topScreenPos.x - playerBoxHalfWidth;
  const float barTopOffset = 4.f;
  const float barPositionY = topScreenPos.y + playerBoxHeight + barTopOffset;
  const float barVisualHeight = 2.f;
  const float backgroundVisualHeight = 4.f;
  const float backgroundBorderOffsetY = 1.f;

  const std::string armorValueText = std::to_string(static_cast<int>(animatedArmor));

  const ImVec4 armorBarColor = Esp::Armor::Bar::Color;
  const ImVec4 barBackgroundColor = {0.f, 0.f, 0.f, 1.f};

  if (Esp::Armor::Bar::Glow::enabled) {
    const ImVec4 glowEffectColor = {
      armorBarColor.x,
      armorBarColor.y,
      armorBarColor.z,
      0.7f
    };
    const int glowBlurRadius = 7;
    const int glowSpread = 2;
    Render::DrawRectGlow(
      barStartX,
      barPositionY,
      barFullVisualWidth,
      barVisualHeight,
      armorBarColor,
      glowEffectColor,
      glowBlurRadius,
      glowSpread
    );
  }

  const float bgRectX = barStartX;
  const float bgRectY = barPositionY - backgroundBorderOffsetY;
  const float bgRectWidth = barFullVisualWidth + 2.f;
  const float bgRectHeight = backgroundVisualHeight;
  Render::DrawRectFilled(bgRectX, bgRectY, bgRectWidth, bgRectHeight, barBackgroundColor, 0);

  const float filledBarRenderX = barStartX + 1.f;
  Render::DrawRectFilled(filledBarRenderX, barPositionY, filledBarActualWidth, barVisualHeight, armorBarColor, 0);

  if (Esp::Armor::Value::enabled && animatedArmor < 100) {
    ImGui::PushFont(espfont);
    const float armorTextHorizontalOffset = 3.f;
    const float textRenderPosX = barStartX + filledBarActualWidth - armorTextHorizontalOffset;
    const float textRenderPosY = topScreenPos.y + playerBoxHeight + 2.f;
    const ImVec4 armorTextColor = Esp::Armor::Value::Color;

    Render::Text(textRenderPosX, textRenderPosY, armorTextColor, armorValueText, fontSize, font_flags_t::outline);
    ImGui::PopFont();
  }
}

void ESP::DrawPlayerInfo(const view_matrix_t& viewmatrix, const Vector& screenHead, const Vector& screenFeet, std::string PlayerName, uint32_t PlayerFlags, uint16_t ItemDefinitionIndex, bool isEnemy) {
  const float boxHeight = screenFeet.y - screenHead.y;
  const float boxHalfVisualWidth = boxHeight * 0.25f;

  const char* cstrItemName = GetItemName(ItemDefinitionIndex);
  const std::string currentItemName = (cstrItemName == nullptr) ? "N/A" : cstrItemName;
  const std::string weaponIconGlyph = GunIcon(cstrItemName);

  constexpr uint32_t IN_AIR_FLAG_VALUE = (1 << 0);
  const bool isPlayerGrounded = !(PlayerFlags & IN_AIR_FLAG_VALUE);
  const std::string playerAirGroundState = isPlayerGrounded ? "OnGround" : "InAir";

  const float nameTextPosX = (screenHead.x + boxHalfVisualWidth) + 3.0f;
  const float nameTextPosY = screenHead.y;

  ImGui::PushFont(espfont);
  const float itemNameTextWidth = ImGui::CalcTextSize(currentItemName.c_str()).x;
  ImGui::PopFont();
  const float itemNameTextBaseX = screenHead.x;
  const float itemNameTextPosX = itemNameTextBaseX - (itemNameTextWidth / 2.0f);
  const float itemNameTextPosY = screenHead.y + boxHeight + 9.0f;

  ImGui::PushFont(gunIcons);
  const float weaponIconGlyphWidth = ImGui::CalcTextSize(weaponIconGlyph.c_str()).x;
  ImGui::PopFont();
  const float weaponIconBaseX = screenHead.x;
  const float weaponIconPosX = weaponIconBaseX - (weaponIconGlyphWidth / 2.0f);
  const float weaponIconPosY = screenHead.y + boxHeight + 17.0f;

  const ImVec4 infoElementColor = Esp::Info::Color;
  const float defaultFontSize = fontSize;
  const float iconDisplayFontSize = 18.0f;
  const font_flags_t textRenderStyle = font_flags_t::outline;

  ImGui::PushFont(espfont);
  Render::Text(nameTextPosX, nameTextPosY, infoElementColor, PlayerName, defaultFontSize, textRenderStyle);
  Render::Text(itemNameTextPosX, itemNameTextPosY, infoElementColor, currentItemName, defaultFontSize, textRenderStyle);
  ImGui::PopFont();

  ImGui::PushFont(gunIcons);
  Render::Gun(weaponIconPosX, weaponIconPosY, infoElementColor, weaponIconGlyph, iconDisplayFontSize, textRenderStyle);
  ImGui::PopFont();
}

void ESP::DrawViewline(const view_matrix_t& viewmatrix, const Vector& worldViewAngles, const Vector& worldEntityHeadPos, bool isEnemy) {
  const float viewLineRenderLength = Esp::Viewline::length;
  const float viewLineStartOffset = 10.f;
  const float lineRenderThickness = 1.0f;
  const ImVec4 viewLineColor = Esp::Viewline::Color;

  const float dotSquareHalfDim = 1.5f;
  const float dotLineRenderThickness = 1.0f;
  const ImVec4 dotSquareColor = Esp::Viewline::Dot::Color;

  const float pitchInRadians = worldViewAngles.x * static_cast<float>(M_PI) / 180.f;
  const float yawInRadians = worldViewAngles.y * static_cast<float>(M_PI) / 180.f;

  const Vector directionVector = {
    cosf(yawInRadians) * cosf(pitchInRadians),
    sinf(yawInRadians) * cosf(pitchInRadians),
    -sinf(pitchInRadians)
  };

  // FIXED: Removed const from lineWorldStartPos and lineWorldEndPos
  Vector lineWorldStartPos = worldEntityHeadPos + directionVector * viewLineStartOffset;
  Vector lineWorldEndPos = worldEntityHeadPos + directionVector * (viewLineStartOffset + viewLineRenderLength);

  Vector lineScreenStartPos, lineScreenEndPos;
  if (Vector::world_to_screen(viewmatrix, lineWorldStartPos, lineScreenStartPos) && // Using parameter viewmatrix
    Vector::world_to_screen(viewmatrix, lineWorldEndPos, lineScreenEndPos)) { // Using parameter viewmatrix

    Render::AALine(lineScreenStartPos.x, lineScreenStartPos.y, lineScreenEndPos.x, lineScreenEndPos.y, viewLineColor, lineRenderThickness);

    if (Esp::Viewline::Dot::enabled) {
      // FIXED: Reverted to original-style calculation for right/up vectors for the dot, avoiding normalize/cross
      Vector dotOrientationRight = {
        -sinf(yawInRadians),
        cosf(yawInRadians),
        0.f
      };
      Vector dotOrientationUp = {
        cosf(yawInRadians) * sinf(pitchInRadians),
        sinf(yawInRadians) * sinf(pitchInRadians),
        cosf(pitchInRadians)
      };

      Vector worldCornerFTR = lineWorldEndPos + dotOrientationRight * dotSquareHalfDim + dotOrientationUp * dotSquareHalfDim;
      Vector worldCornerFTL = lineWorldEndPos - dotOrientationRight * dotSquareHalfDim + dotOrientationUp * dotSquareHalfDim;
      Vector worldCornerFBR = lineWorldEndPos + dotOrientationRight * dotSquareHalfDim - dotOrientationUp * dotSquareHalfDim;
      Vector worldCornerFBL = lineWorldEndPos - dotOrientationRight * dotSquareHalfDim - dotOrientationUp * dotSquareHalfDim;

      Vector screenCornerFTR, screenCornerFTL, screenCornerFBR, screenCornerFBL;
      const bool areDotPointsOnScreen =
        Vector::world_to_screen(viewmatrix, worldCornerFTR, screenCornerFTR) && // Using parameter viewmatrix
        Vector::world_to_screen(viewmatrix, worldCornerFTL, screenCornerFTL) && // Using parameter viewmatrix
        Vector::world_to_screen(viewmatrix, worldCornerFBR, screenCornerFBR) && // Using parameter viewmatrix
        Vector::world_to_screen(viewmatrix, worldCornerFBL, screenCornerFBL);   // Using parameter viewmatrix

      if (areDotPointsOnScreen &&
        screenCornerFTR.x != 0.f && screenCornerFTR.y != 0.f && screenCornerFTR.z != 0.f && // Comparing floats to 0.f
        screenCornerFTL.x != 0.f && screenCornerFTL.y != 0.f && screenCornerFTL.z != 0.f &&
        screenCornerFBR.x != 0.f && screenCornerFBR.y != 0.f && screenCornerFBR.z != 0.f &&
        screenCornerFBL.x != 0.f && screenCornerFBL.y != 0.f && screenCornerFBL.z != 0.f) {

        Render::AALine(screenCornerFTL.x, screenCornerFTL.y, screenCornerFTR.x, screenCornerFTR.y, dotSquareColor, dotLineRenderThickness);
        Render::AALine(screenCornerFTR.x, screenCornerFTR.y, screenCornerFBR.x, screenCornerFBR.y, dotSquareColor, dotLineRenderThickness);
        Render::AALine(screenCornerFBR.x, screenCornerFBR.y, screenCornerFBL.x, screenCornerFBL.y, dotSquareColor, dotLineRenderThickness);
        Render::AALine(screenCornerFBL.x, screenCornerFBL.y, screenCornerFTL.x, screenCornerFTL.y, dotSquareColor, dotLineRenderThickness);
      }
    }
  }
}

void ESP::DrawPlayerSkeleton(const view_matrix_t& viewmatrix, uint64_t playerBoneArray, bool isEnemy) {
  auto* gameDriver = GameVars::getInstance()->getDriver();

  const ImVec4 skeletonLineColor = Esp::Skeleton::Color;
  const float skeletonLineThickness = 1.0f;

  constexpr size_t numBoneConnections = sizeof(bConnections) / sizeof(bConnections[0]);
  std::vector<Vector> screenBone1Positions;
  std::vector<Vector> screenBone2Positions;
  screenBone1Positions.reserve(numBoneConnections);
  screenBone2Positions.reserve(numBoneConnections);

  for (const auto& bonePairIndices : bConnections) {
    const uintptr_t bone1MemoryAddress = playerBoneArray + static_cast<uintptr_t>(bonePairIndices.bone1) * 32;
    const uintptr_t bone2MemoryAddress = playerBoneArray + static_cast<uintptr_t>(bonePairIndices.bone2) * 32;

    Vector worldBone1Pos = driver::read_memory<Vector>(gameDriver, bone1MemoryAddress);
    Vector worldBone2Pos = driver::read_memory<Vector>(gameDriver, bone2MemoryAddress);

    Vector boneSegmentVector = worldBone1Pos - worldBone2Pos;
    float boneLength = sqrtf(
      powf(boneSegmentVector.x, 2.f) +
      powf(boneSegmentVector.y, 2.f) +
      powf(boneSegmentVector.z, 2.f)
    );

    Vector screenBone1Pos, screenBone2Pos;
    if (boneLength < MAX_BONE_LENGTH &&
      boneLength > 0.0f &&
      Vector::world_to_screen(viewmatrix, worldBone1Pos, screenBone1Pos) && // Using parameter viewmatrix
      Vector::world_to_screen(viewmatrix, worldBone2Pos, screenBone2Pos)) {  // Using parameter viewmatrix

      screenBone1Positions.push_back(screenBone1Pos);
      screenBone2Positions.push_back(screenBone2Pos);
    }
  }

  for (size_t i = 0; i < screenBone1Positions.size(); ++i) {
    const Vector& p1OnScreen = screenBone1Positions[i];
    const Vector& p2OnScreen = screenBone2Positions[i];
    Render::AALine(p1OnScreen.x, p1OnScreen.y, p2OnScreen.x, p2OnScreen.y, skeletonLineColor, skeletonLineThickness);
  }
}

void ESP::DrawPlayerJoints(const view_matrix_t& viewmatrix, uint64_t playerBoneArray, bool isEnemy) {
  auto* gameDriver = GameVars::getInstance()->getDriver();
  ImVec4 jointDotColor = Esp::Skeleton::Dots::Color;
  float configuredRadiusFactor = Esp::Skeleton::Dots::radius;

  float dynamicBaseRadius = 0.0f; // Entspricht dem initialen Radius, wenn Neck.y/Spine.y = 0

  std::vector<Vector> screenJointPositions;
  screenJointPositions.reserve(sizeof(bPoints) / sizeof(bPoints[0]));

  // Phase 1: Sammle Gelenk-Bildschirmpositionen und aktualisiere den dynamicBaseRadius.
  // dynamicBaseRadius wird durch die letzte erfolgreiche Nacken/Wirbels�ulen-Transformation bestimmt.
  for (const auto& jointPointDef : bPoints) {
    uintptr_t boneAddr = playerBoneArray + static_cast<uintptr_t>(jointPointDef.bone) * 32;
    Vector worldJointPos = driver::read_memory<Vector>(gameDriver, boneAddr);
    Vector screenJointPos;

    if (Vector::world_to_screen(viewmatrix, worldJointPos, screenJointPos)) {
      screenJointPositions.push_back(screenJointPos);
    }

    // Aktualisiere dynamicBaseRadius basierend auf der aktuellen Nacken/Wirbels�ulen-Transformation
    Vector worldNeck = driver::read_memory<Vector>(gameDriver, playerBoneArray + bones::neck * 32);
    Vector worldSpine = driver::read_memory<Vector>(gameDriver, playerBoneArray + bones::spine * 32);
    Vector currentScreenNeck, currentScreenSpine;

    if (Vector::world_to_screen(viewmatrix, worldNeck, currentScreenNeck) &&
      Vector::world_to_screen(viewmatrix, worldSpine, currentScreenSpine)) {
      // Nur wenn BEIDE erfolgreich sind, wird der Radius aktualisiert.
      // Dies entspricht der originalen Logik mit den 'continue'-Spr�ngen.
      dynamicBaseRadius = std::abs(currentScreenNeck.y - currentScreenSpine.y);
    }
    // Wenn die Transformation von Nacken/Wirbels�ule fehlschl�gt, beh�lt dynamicBaseRadius
    // seinen Wert aus einer vorherigen erfolgreichen Iteration oder den Initialwert (0.0f).
  }

  // Phase 2: Zeichne alle Gelenkpunkte mit dem final bestimmten dynamicBaseRadius.
  for (const auto& screenPos : screenJointPositions) {
    Render::AADot(screenPos.x, screenPos.y, dynamicBaseRadius * configuredRadiusFactor, jointDotColor);
    if (configuredRadiusFactor <= 0.4f) { // Originale Bedingung f�r den zweiten Punkt
      Render::AADot(screenPos.x, screenPos.y, 0.5f, jointDotColor);
    }
  }
}

void ESP::DrawPlayerHead(const view_matrix_t& viewmatrix, uint64_t playerBoneArray, bool isEnemy) {
  auto* gameDriver = GameVars::getInstance()->getDriver();

  const uintptr_t headBoneAddr  = playerBoneArray + static_cast<uintptr_t>(bones::head) * 32;
  const uintptr_t neckBoneAddr  = playerBoneArray + static_cast<uintptr_t>(bones::neck) * 32;
  const uintptr_t spineBoneAddr = playerBoneArray + static_cast<uintptr_t>(bones::spine) * 32;

  Vector worldHeadPos  = driver::read_memory<Vector>(gameDriver, headBoneAddr);
  Vector worldNeckPos  = driver::read_memory<Vector>(gameDriver, neckBoneAddr);
  Vector worldSpinePos = driver::read_memory<Vector>(gameDriver, spineBoneAddr);

  Vector screenHead, screenNeck, screenSpine;
  if (Vector::world_to_screen(viewmatrix, worldHeadPos, screenHead) &&    // Using parameter viewmatrix
    Vector::world_to_screen(viewmatrix, worldNeckPos, screenNeck) &&    // Using parameter viewmatrix
    Vector::world_to_screen(viewmatrix, worldSpinePos, screenSpine)) {  // Using parameter viewmatrix

    const float headCircleRadius = std::abs(screenNeck.y - screenSpine.y);

    const ImVec4 headCircleColor = Esp::Skeleton::Head::Color;
    const float circleLineThickness = 1.0f;

    Render::AACircle(screenHead.x, screenHead.y, headCircleRadius, headCircleColor, circleLineThickness);
  }
}

void ESP::DrawPlayerSnapline(const view_matrix_t& viewmatrix, const Vector& screenHead, const Vector& screenFeet, bool isEnemy) {
  const int systemScreenWidth = GetSystemMetrics(SM_CXSCREEN);

  const float lineStartXCoord = static_cast<float>(systemScreenWidth / 2);
  const float lineStartYCoord = 0.f;

  const float lineEndXCoord = screenHead.x;
  const float lineEndYCoord = screenHead.y;

  const ImVec4 snaplineColor = Esp::Snapline::Color;
  const float snaplineThickness = Esp::Snapline::thickness;

  Render::AALine(lineStartXCoord, lineStartYCoord, lineEndXCoord, lineEndYCoord, snaplineColor, snaplineThickness);
}

void ESP::DrawProjectile(const view_matrix_t& viewmatrix, const Vector& screenPos, std::string name, bool isEnemy) {
  ImGui::PushFont(espfont);

  const float projectileNameTextWidth = ImGui::CalcTextSize(name.c_str()).x;

  const float textRenderPosX = screenPos.x - (projectileNameTextWidth / 2.f);
  const float textRenderPosY = screenPos.y;

  const ImVec4 projectileNameColor = Projectile::Color;
  const font_flags_t textRenderingFlags = font_flags_t::outline;

  Render::Text(
    textRenderPosX,
    textRenderPosY,
    projectileNameColor,
    name,
    fontSize,
    textRenderingFlags
  );

  ImGui::PopFont();
}