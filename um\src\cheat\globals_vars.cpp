#include "../pch.h"
#include "globals.hpp"

// Definition of external variables from globals.hpp
namespace globals {
    float offsetTexthealth;
    float window_width;
    float window_height;
    bool isFirstFrameKeystrokes;
    float pos_x;
    float pos_y;

    namespace Team {
        bool ESP;
    }

    namespace Enemy {
        bool ESP;
    }

    std::string playerStateString;
    int HeightCfgEnemy, HeightCfgTeam;
}

// Definition of external variables from overlayrender.cpp
float flHitmarkerAlpha = 1.0f;
int EnemyEspSettingsPage = 0;
int TeamEspSettingsPage = 0;
int EspSettingsPage = 0;  // Added missing EspSettingsPage
int MiscSettingsPage = 0;
ImVec2 childPos;
ImVec2 childScreenPos;
ImVec2 childWindowSize;



// Definition of other namespace variables from overlayrender.cpp
namespace Legitbot {
    bool enabled = true;
    bool teamcheck = true;
    bool visiblecheck = true;
    float smoothness = 1.0f;
    float radius = 99.9f;

    namespace Circle {
        bool enabled = true;
        bool filled = true;
        ImVec4 Color = ImVec4(1.0f, 0.0f, 0.0f, 1.0f);
    }
}

namespace Triggerbot {
    bool enabled = false;
    int hotkey = VK_LMENU;
}

namespace DarkMode {
    bool enabled = false;
    float alpha = 50.0f;
}

namespace Projectile {
    bool enabled = false;
    bool box = false;
    bool name = false;
    bool line = false;
    bool erase = false;
    ImVec4 Color = ImVec4(1.0f, 1.0f, 1.0f, 1.0f);
}

namespace Smoke {
    bool enabled = false;

    namespace name {
        bool enabled = false;
        ImVec4 Color = ImVec4(1.0f, 1.0f, 1.0f, 1.0f);
    }

    namespace circle {
        bool enabled = false;
        ImVec4 Color = ImVec4(1.0f, 1.0f, 1.0f, 1.0f);
    }

    namespace countdown {
        bool enabled = false;

        namespace bar {
            bool enabled = false;
            ImVec4 Color = ImVec4(1.0f, 1.0f, 1.0f, 1.0f);
        }

        namespace text {
            bool enabled = false;
            ImVec4 Color = ImVec4(1.0f, 1.0f, 1.0f, 1.0f);
        }
    }
}

namespace Crosshair {
    bool enabled = false;
    ImVec4 Color = ImVec4(1.0f, 1.0f, 1.0f, 1.0f);
    float thickness = 1.0f;
    float length = 10.0f;
    float gap = 5.0f;
    bool dotenabled = false;
    float dotSize = 2.0f;
}

namespace Hitmarker {
    bool enabled = false;
    ImVec4 Color = ImVec4(1.0f, 1.0f, 1.0f, 1.0f);
    float thickness = 1.0f;
    float length = 10.0f;
    float gap = 5.0f;
    float duration = 1000.0f;
}

namespace Keystrokes {
    bool enabled = false;
    ImVec4 Color = ImVec4(1.0f, 1.0f, 1.0f, 0.25f);
    ImVec4 pressedColor = ImVec4(1.0f, 1.0f, 1.0f, 1.0f);
}

namespace MenuOutline {
    ImVec4 Color = ImVec4(1.0f, 1.0f, 1.0f, 1.0f);
}
